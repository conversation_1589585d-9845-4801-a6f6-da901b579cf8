import React, { useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { useQueryClient } from "@tanstack/react-query";
import {
  Button,
  Drawer,
  Input,
  Select,
  Text,
  toast,
  Toaster,
} from "@camped-ai/ui";
import { format, addDays } from "date-fns";
import { RoomInventoryStatus } from "../types/booking";
import { Booking, Room } from "../types";
import { getStatusDisplayName } from "./statusUtils";
import TimelineHeader from "./TimelineHeader";
import RoomRow from "./RoomRow";
import RoomList from "./RoomList";
import FilterControls from "./FilterControls";
import TimelineFooter from "./TimelineFooter";
import { useTimelineData } from "./hooks/useTimelineData";
import { useScrollSync } from "./hooks/useScrollSync";
import SimpleBookingDetail from "../../booking/simple-booking-detail";
import UnallocatedBookings from "./UnallocatedBookings";
import UnallocatedBookingsButton from "./UnallocatedBookingsButton";
import SplitBookingModal from "./SplitBookingModal";
import "./timelineStyles.css";

interface TimelineViewProps {
  hotelId?: string;
}

// Main Timeline View Component
const TimelineView: React.FC<TimelineViewProps> = ({
  hotelId: propHotelId,
}) => {
  const { slug } = useParams();
  const queryClient = useQueryClient();

  // Unallocated bookings state
  const [unallocatedBookings, setUnallocatedBookings] = useState<any[]>([]);
  const [isUnallocatedDrawerOpen, setIsUnallocatedDrawerOpen] = useState(false);

  // Booking detail drawer state
  const [selectedBookingId, setSelectedBookingId] = useState<string | null>(
    null
  );
  const [isBookingDrawerOpen, setIsBookingDrawerOpen] = useState(false);

  // Booking selection state for timeline interactions
  const [selectedTimelineBookingId, setSelectedTimelineBookingId] = useState<
    string | null
  >(null);

  // Room allocation modal state
  const [isAllocationModalOpen, setIsAllocationModalOpen] = useState(false);
  const [selectedBookingForAllocation, setSelectedBookingForAllocation] =
    useState<any>(null);
  const [availableRoomsForAllocation, setAvailableRoomsForAllocation] =
    useState<Room[]>([]);
  const [selectedTargetRoomId, setSelectedTargetRoomId] = useState<string>("");

  // Update status drawer state
  const [selectedBookingForUpdate, setSelectedBookingForUpdate] =
    useState<Booking | null>(null);
  const [isUpdateStatusDrawerOpen, setIsUpdateStatusDrawerOpen] =
    useState(false);
  const [isUpdatingBooking, setIsUpdatingBooking] = useState(false);
  const [updateForm, setUpdateForm] = useState({
    status: "",
    fromDate: "",
    toDate: "",
    notes: "",
  });

  // Split booking modal state
  const [isSplitBookingModalOpen, setIsSplitBookingModalOpen] = useState(false);
  const [selectedBookingForSplit, setSelectedBookingForSplit] =
    useState<Booking | null>(null);
  const [selectedRoomForSplit, setSelectedRoomForSplit] = useState<Room | null>(
    null
  );

  // Stabilize hotelId to prevent unnecessary re-renders
  const hotelId = useMemo(() => {
    return propHotelId || slug;
  }, [propHotelId, slug]);

  const {
    selectedRoomTypes,
    setSelectedRoomTypes,
    selectedRooms,
    setSelectedRooms,
    selectedStatuses,
    setSelectedStatuses,
    hoveredRoomId,
    setHoveredRoomId,

    startDate,
    setStartDate,
    endDate,
    setEndDate,
    dateSlots,
    groupedRooms,
    weekBookings,
    availableBlocks,
    roomTypes,
    roomTypeConfigs,
    rooms,
    TIMELINE_WIDTH,
    isLoading,
    error,
    hasRealData,
    rawApiData,
    refreshData,
  } = useTimelineData(hotelId);

  // Handle apply date range - refresh data with new dates
  const handleApplyDateRange = (newStartDate: Date, newEndDate: Date) => {
    refreshData?.(newStartDate, newEndDate);
  };

  // Handle view booking - open booking detail drawer
  const handleViewBooking = (bookingId: string) => {
    setSelectedBookingId(bookingId);
    setIsBookingDrawerOpen(true);
  };

  // Handle update status - open update status drawer
  const handleUpdateStatus = (booking: Booking) => {
    setSelectedBookingForUpdate(booking);
    setUpdateForm({
      status: booking.status,
      fromDate: format(booking.checkIn, "yyyy-MM-dd"),
      toDate: format(booking.checkOut, "yyyy-MM-dd"),
      notes: booking.notes || "",
    });
    setIsUpdateStatusDrawerOpen(true);
  };

  // Handle booking click - toggle selection
  const handleBookingClick = (booking: Booking) => {
    const bookingId = booking.order_id || booking.id;
    setSelectedTimelineBookingId(
      selectedTimelineBookingId === bookingId ? null : bookingId
    );
  };

  // Handle split booking - open split booking modal
  const handleSplitBooking = (booking: Booking, room: Room) => {
    setSelectedBookingForSplit(booking);
    setSelectedRoomForSplit(room);
    setIsSplitBookingModalOpen(true);
  };

  // Handle split booking success - refresh data
  const handleSplitBookingSuccess = () => {
    refreshData?.(startDate, endDate);
    setSelectedBookingForSplit(null);
    setSelectedRoomForSplit(null);
  };

  // Handle mark as maintenance (for available rooms)
  const handleMarkAsMaintenance = async (booking: Booking) => {
    try {
      // Use the bulk-update endpoint for status changes
      const response = await fetch(
        `/admin/hotel-management/room-inventory/bulk-update`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            room_id: booking.room_id,
            start_date: format(booking.checkIn, "yyyy-MM-dd"),
            end_date: format(booking.checkOut, "yyyy-MM-dd"),
            status: "maintenance",
            metadata: {
              notes: `Marked as maintenance on ${new Date().toISOString()}`,
            },
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to mark as maintenance");
      }

      // Refresh the timeline data to show updated status
      await queryClient.invalidateQueries({
        queryKey: ["hotel-availability", hotelId],
      });

      toast.success("Success", {
        description: "Room marked as maintenance successfully",
      });
    } catch (error) {
      console.error("Error marking as maintenance:", error);

      toast.error("Error", {
        description:
          error instanceof Error
            ? error.message
            : "Failed to mark as maintenance",
      });
    }
  };

  // Handle mark as available (for maintenance rooms)
  const handleMarkAsAvailable = async (booking: Booking) => {
    try {
      // Use the bulk-update endpoint for status changes
      const response = await fetch(
        `/admin/hotel-management/room-inventory/bulk-update`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            room_id: booking.room_id,
            start_date: format(booking.checkIn, "yyyy-MM-dd"),
            end_date: format(booking.checkOut, "yyyy-MM-dd"),
            status: "available",
            metadata: {
              notes: `Marked as available on ${new Date().toISOString()}`,
            },
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to mark as available");
      }

      // Refresh the timeline data to show updated status
      await queryClient.invalidateQueries({
        queryKey: ["hotel-availability", hotelId],
      });

      toast.success("Success", {
        description: "Room marked as available successfully",
      });
    } catch (error) {
      console.error("Error marking as available:", error);

      toast.error("Error", {
        description:
          error instanceof Error
            ? error.message
            : "Failed to mark as available",
      });
    }
  };

  // Handle move to unallocated
  const handleMoveToUnallocated = async (booking: Booking, room: Room) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] 🚀 UNALLOCATION START:`, {
      bookingId: booking.id,
      orderId: booking.order_id,
      roomId: room.id,
      roomName: room.name,
      roomConfigId: room.room_config_id,
      guestName: booking.guestName,
      checkIn: booking.checkIn,
      checkOut: booking.checkOut,
      currentUnallocatedCount: unallocatedBookings.length,
    });

    // Check if this booking has a valid order_id
    // Only check booking.order_id - do NOT use the synthetic id field
    const orderId = booking.order_id;

    if (!orderId || orderId === "" || !orderId.startsWith("order_")) {
      console.log(
        `[${timestamp}] ❌ UNALLOCATION VALIDATION FAILED - INVALID ORDER ID:`,
        {
          orderId,
          hasOrderId: !!orderId,
          orderIdEmpty: orderId === "",
          startsWithOrder: orderId?.startsWith("order_"),
          bookingId: booking.id,
          bookingStatus: booking.status,
        }
      );
      toast.error("Error", {
        description:
          "This booking cannot be unallocated as it doesn't have a valid order ID. Only bookings created through orders can be unallocated.",
      });
      return;
    }

    // Check if the booking status allows unallocation
    const unallocatableStatuses = [
      RoomInventoryStatus.RESERVED,
      RoomInventoryStatus.BOOKED,
      RoomInventoryStatus.RESERVED_UNASSIGNED,
    ];

    if (!unallocatableStatuses.includes(booking.status)) {
      console.log(
        `[${timestamp}] ❌ UNALLOCATION VALIDATION FAILED - INVALID STATUS:`,
        {
          bookingStatus: booking.status,
          allowedStatuses: unallocatableStatuses,
          bookingId: booking.id,
          orderId,
        }
      );
      toast.error("Error", {
        description: `This booking cannot be unallocated. Only bookings with status "Reserved" or "Booked" can be unallocated. Current status: "${getStatusDisplayName(
          booking.status
        )}".`,
      });
      return;
    }

    const bookingId = orderId;
    console.log(`[${timestamp}] ✅ UNALLOCATION VALIDATION PASSED:`, {
      bookingId,
      orderId,
    });

    try {
      // Format dates for API call
      const fromDate = format(booking.checkIn, "yyyy-MM-dd");
      const toDate = format(booking.checkOut, "yyyy-MM-dd");

      const requestPayload = {
        inventory_id: booking.id,
        room_id: room.id,
        order_id: bookingId,
        from_date: fromDate,
        to_date: toDate,
      };

      console.log(`[${timestamp}] 📤 UNALLOCATION API REQUEST:`, {
        url: `/admin/hotel-management/room-inventory/unallocate`,
        method: "POST",
        payload: requestPayload,
        hotelId,
      });

      // Call the API to unallocate the room
      const response = await fetch(
        `/admin/hotel-management/room-inventory/unallocate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include", // Include cookies for session auth
          body: JSON.stringify(requestPayload),
        }
      );

      console.log(`[${timestamp}] 📥 UNALLOCATION API RESPONSE:`, {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        headers: Object.fromEntries(response.headers.entries()),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.log(`[${timestamp}] ❌ UNALLOCATION API ERROR:`, {
          status: response.status,
          errorData,
        });
        throw new Error(errorData.message || "Failed to unallocate room");
      }

      const responseData = await response.json();
      console.log(`[${timestamp}] ✅ UNALLOCATION API SUCCESS:`, {
        responseData,
      });

      console.log(`[${timestamp}] 🔄 INVALIDATING QUERY CACHE:`, {
        queryKey: ["hotel-availability", hotelId],
        hotelId,
      });

      // Refresh the data by invalidating the query cache
      await queryClient.invalidateQueries({
        queryKey: ["hotel-availability", hotelId],
      });

      console.log(`[${timestamp}] ✅ QUERY CACHE INVALIDATED SUCCESSFULLY`);

      // Add a small delay to ensure backend processing is complete
      console.log(`[${timestamp}] ⏳ WAITING FOR BACKEND PROCESSING...`);
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Force a manual refetch to ensure we get the latest data
      console.log(`[${timestamp}] 🔄 FORCING MANUAL DATA REFETCH...`);
      await refreshData?.(startDate, endDate);

      toast.success("Success", {
        description: "Room has been unallocated successfully",
      });

      console.log(`[${timestamp}] 🎉 UNALLOCATION COMPLETED SUCCESSFULLY`);
    } catch (error) {
      const errorTimestamp = new Date().toISOString();
      console.error(`[${errorTimestamp}] ❌ UNALLOCATION ERROR:`, {
        error,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
        errorStack: error instanceof Error ? error.stack : undefined,
        bookingId,
        roomId: room.id,
      });

      toast.error("Error", {
        description:
          error instanceof Error ? error.message : "Failed to unallocate room",
      });

      // Fallback to local state for demo if API call fails
      const formattedDate = format(booking.checkIn, "yyyy-MM-dd");
      const nextDay = addDays(booking.checkOut, 0).toISOString().split("T")[0];

      // Create a new unallocated booking entry
      const newUnallocatedBooking = {
        id: `unallocated_${Date.now()}`,
        status: "reserved",
        room_id: null,
        from_date: formattedDate,
        to_date: nextDay,
        notes: `order_${bookingId} room_config_id:${room.room_config_id} room_type:${room.name}`,
        room_config_id: room.room_config_id,
        order_id: bookingId,
        guest_name: booking.guestName,
        check_in_date: formattedDate,
        check_out_date: nextDay,
        metadata: {
          room_config_id: room.room_config_id,
          check_in_date: new Date(formattedDate),
          check_out_date: new Date(nextDay),
        },
      };

      console.log(`[${errorTimestamp}] 🔄 FALLBACK: Adding to local state:`, {
        newUnallocatedBooking,
        currentUnallocatedCount: unallocatedBookings.length,
      });

      // Add to local unallocated bookings
      setUnallocatedBookings((prev) => {
        const updated = [...prev, newUnallocatedBooking];
        console.log(`[${errorTimestamp}] 📝 LOCAL STATE UPDATED:`, {
          previousCount: prev.length,
          newCount: updated.length,
          addedBooking: newUnallocatedBooking,
        });
        return updated;
      });

      toast.info("Info", {
        description: "Using local state as fallback (demo mode)",
      });

      console.log(`[${errorTimestamp}] 🔄 FALLBACK COMPLETED`);
    }
  };

  // Get available rooms for a specific date range and room type
  const getAvailableRoomsForDate = (
    fromDate: Date,
    toDate: Date,
    roomConfigId: string
  ): Room[] => {
    return rooms.filter((room) => {
      // Filter by room type
      if (room.room_config_id !== roomConfigId) return false;

      // Check if room is available for the entire date range
      const dateRange = [];
      let currentDate = new Date(fromDate);
      while (currentDate <= toDate) {
        dateRange.push(new Date(currentDate));
        currentDate.setDate(currentDate.getDate() + 1);
      }

      // Check availability for each date in the range
      return dateRange.every((date) => {
        const formattedDate = format(date, "yyyy-MM-dd");
        const roomAvailability = availableBlocks.find(
          (block) =>
            block.room_id === room.id &&
            format(block.date, "yyyy-MM-dd") === formattedDate
        );
        return roomAvailability?.status === "available";
      });
    });
  };

  // Handle assign room button click
  const handleAssignRoom = (booking: any) => {
    if (!booking.metadata?.room_config_id) {
      toast.error("Error", {
        description: "Room configuration not found for this booking",
      });
      return;
    }

    const availableRooms = getAvailableRoomsForDate(
      booking.metadata.check_in_date,
      booking.metadata.check_out_date,
      booking.metadata.room_config_id
    );

    setSelectedBookingForAllocation(booking);
    setAvailableRoomsForAllocation(availableRooms);
    setSelectedTargetRoomId("");
    setIsAllocationModalOpen(true);
  };

  // Handle room allocation API call
  const handleAllocateRoom = async () => {
    if (!selectedBookingForAllocation || !selectedTargetRoomId) return;

    const bookingId =
      selectedBookingForAllocation.order_id || selectedBookingForAllocation.id;
    if (!bookingId) {
      toast.error("Error", {
        description: "No booking ID found for this booking",
      });
      return;
    }

    try {
      const fromDate = format(
        selectedBookingForAllocation.metadata.check_in_date,
        "yyyy-MM-dd"
      );
      const toDate = format(
        selectedBookingForAllocation.metadata.check_out_date,
        "yyyy-MM-dd"
      );

      // Call the API to allocate the room
      const requestBody = {
        inventory_item_id: selectedTargetRoomId,
        room_id: selectedTargetRoomId,
        order_id: bookingId,
        from_date: fromDate,
        to_date: toDate,
        status: "booked",
        inventory_id: selectedBookingForAllocation.id,
      };

      const response = await fetch(
        `/admin/hotel-management/room-inventory/allocate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        }
      );

      if (!response.ok) {
        let errorMessage = "Failed to allocate room";
        try {
          const errorData = await response.json();
          console.error("API error response:", errorData);
          errorMessage = errorData.message || errorMessage;
        } catch (parseError) {
          console.error("Error parsing error response:", parseError);
        }
        throw new Error(errorMessage);
      }

      await response.json();

      // Remove from unallocated bookings
      setUnallocatedBookings((prev) =>
        prev.filter((booking) => booking.id !== selectedBookingForAllocation.id)
      );

      // Refresh the data
      await queryClient.invalidateQueries({
        queryKey: ["hotel-availability", hotelId],
      });

      toast.success("Success", {
        description: "Room has been allocated successfully",
      });

      // Close the modal
      setIsAllocationModalOpen(false);
      setSelectedBookingForAllocation(null);
      setSelectedTargetRoomId("");
    } catch (error) {
      console.error("Error allocating room:", error);

      toast.error("Error", {
        description:
          error instanceof Error ? error.message : "Failed to allocate room",
      });
    }
  };

  // Handle update booking API call
  const handleUpdateBooking = async () => {
    if (!selectedBookingForUpdate) return;

    setIsUpdatingBooking(true);

    try {
      // Use room inventory bulk update API since we're updating room availability status
      const response = await fetch(
        `/admin/hotel-management/room-inventory/bulk-update`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            room_id: selectedBookingForUpdate.room_id,
            start_date: updateForm.fromDate,
            end_date: updateForm.toDate,
            status: updateForm.status,
            metadata: {
              notes: updateForm.notes,
              updated_at: new Date().toISOString(),
            },
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update booking status");
      }

      await response.json();

      // Close the drawer
      setIsUpdateStatusDrawerOpen(false);
      setSelectedBookingForUpdate(null);

      // Refresh the timeline data to show updated status
      await queryClient.invalidateQueries({
        queryKey: ["hotel-availability", hotelId],
      });

      // Show success toast notification
      toast.success("Success", {
        description: "Room status updated successfully",
      });
    } catch (error) {
      console.error("Error updating booking:", error);
      toast.error("Error", {
        description: `Error updating room status: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      });
    } finally {
      setIsUpdatingBooking(false);
    }
  };

  // Process unallocated bookings from existing data (no additional API call)
  const processUnallocatedBookings = (apiData: any) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] 🔄 PROCESSING UNALLOCATED BOOKINGS:`, {
      hasApiData: !!apiData,
      hasBookings: !!apiData?.bookings,
      bookingsIsArray: Array.isArray(apiData?.bookings),
      bookingsCount: apiData?.bookings?.length || 0,
      hasAvailability: !!apiData?.availability,
      availabilityIsArray: Array.isArray(apiData?.availability),
      availabilityCount: apiData?.availability?.length || 0,
      currentUnallocatedCount: unallocatedBookings.length,
      apiDataKeys: apiData ? Object.keys(apiData) : [],
      rawBookingsData: apiData?.bookings ? apiData.bookings.slice(0, 3) : [], // First 3 bookings for debugging
    });

    if (!apiData?.bookings || !Array.isArray(apiData.bookings)) {
      console.log(
        `[${timestamp}] ❌ NO VALID BOOKINGS DATA - CLEARING UNALLOCATED:`,
        {
          apiData: !!apiData,
          bookings: apiData?.bookings,
          isArray: Array.isArray(apiData?.bookings),
        }
      );
      setUnallocatedBookings([]);
      return;
    }

    // Build a map of order IDs to their allocation status in availability
    const orderAllocationStatus = new Map();
    if (apiData.availability && Array.isArray(apiData.availability)) {
      console.log(`[${timestamp}] 📊 BUILDING ALLOCATION STATUS MAP:`, {
        availabilityCount: apiData.availability.length,
      });

      apiData.availability.forEach((item: any, index: number) => {
        if (item.order_id) {
          // Check if this is an active allocation (not just a stale record)
          const isActiveAllocation =
            item.status === "booked" || item.status === "reserved";
          orderAllocationStatus.set(item.order_id, {
            isPresent: true,
            isActive: isActiveAllocation,
            status: item.status,
          });

          // Special check for the booking we just unallocated
          const isRecentlyUnallocated =
            item.order_id === "order_01K04YRXCPDADGZ1Z8SVCYRJ6Q";

          if (index < 5 || isRecentlyUnallocated) {
            // Log first 5 for debugging, or if it's the booking we just unallocated
            console.log(
              `[${timestamp}] 📋 ALLOCATION STATUS [${index}]${
                isRecentlyUnallocated ? " (RECENTLY UNALLOCATED)" : ""
              }:`,
              {
                orderId: item.order_id,
                status: item.status,
                isActive: isActiveAllocation,
                roomId: item.room_id,
                fromDate: item.from_date,
                toDate: item.to_date,
                isRecentlyUnallocated,
              }
            );
          }
        }
      });

      console.log(`[${timestamp}] ✅ ALLOCATION STATUS MAP BUILT:`, {
        totalMapped: orderAllocationStatus.size,
        orderIds: Array.from(orderAllocationStatus.keys()).slice(0, 10), // First 10
      });
    } else {
      console.log(
        `[${timestamp}] ⚠️ NO AVAILABILITY DATA FOR ALLOCATION STATUS`
      );
    }

    // Trust the API - if it returns unallocated bookings, show them
    // Only do minimal filtering to remove obviously invalid entries
    console.log(`[${timestamp}] 🔍 FILTERING UNALLOCATED BOOKINGS:`, {
      totalBookings: apiData.bookings.length,
    });

    const filteredUnallocatedBookings = apiData.bookings.filter(
      (booking: any, index: number) => {
        // Extract booking ID from notes if available
        let bookingId = null;
        if (booking.notes) {
          const match = booking.notes.match(/\b(order_[a-z0-9]+)/i);
          bookingId = match ? match[1] : null;
        }

        // For unallocated bookings from orders, the order ID is in the 'id' field
        const orderId = booking.order_id || booking.id || bookingId;
        const isValid = !!orderId;

        // Special check for the booking we just unallocated
        const isRecentlyUnallocated =
          orderId === "order_01K04YRXCPDADGZ1Z8SVCYRJ6Q";

        if (index < 5 || isRecentlyUnallocated) {
          // Log first 5 for debugging, or if it's the booking we just unallocated
          console.log(
            `[${timestamp}] 🔍 BOOKING FILTER [${index}]${
              isRecentlyUnallocated ? " (RECENTLY UNALLOCATED)" : ""
            }:`,
            {
              bookingId: booking.id,
              orderId: booking.order_id,
              extractedBookingId: bookingId,
              finalOrderId: orderId,
              isValid,
              notes: booking.notes?.substring(0, 100),
              guestName: booking.guest_name,
              status: booking.status,
              fromDate: booking.from_date,
              toDate: booking.to_date,
              roomId: booking.room_id,
              isRecentlyUnallocated,
            }
          );
        }

        // Keep the booking if it has a valid order ID
        // Trust that the API has already filtered correctly
        return isValid;
      }
    );

    console.log(`[${timestamp}] ✅ UNALLOCATED BOOKINGS FILTERED:`, {
      originalCount: apiData.bookings.length,
      filteredCount: filteredUnallocatedBookings.length,
      previousUnallocatedCount: unallocatedBookings.length,
      filteredBookings: filteredUnallocatedBookings.map((b) => ({
        id: b.id,
        orderId: b.order_id,
        guestName: b.guest_name,
        fromDate: b.from_date,
        toDate: b.to_date,
      })),
    });

    setUnallocatedBookings((prev) => {
      console.log(`[${timestamp}] 📝 UPDATING UNALLOCATED BOOKINGS STATE:`, {
        previousCount: prev.length,
        newCount: filteredUnallocatedBookings.length,
        previousBookings: prev.map((b) => ({ id: b.id, orderId: b.order_id })),
        newBookings: filteredUnallocatedBookings.map((b) => ({
          id: b.id,
          orderId: b.order_id,
        })),
      });
      return filteredUnallocatedBookings;
    });

    console.log(`[${timestamp}] 🎉 UNALLOCATED BOOKINGS PROCESSING COMPLETED`);
  };

  const {
    roomListRef,
    timelineContentRef,
    handleRoomListScroll,
    handleTimelineScroll,
  } = useScrollSync();

  // Reset scroll position when date range changes
  useEffect(() => {
    if (timelineContentRef.current) {
      timelineContentRef.current.scrollLeft = 0;
    }
    if (roomListRef.current) {
      roomListRef.current.scrollTop = 0;
    }
  }, [startDate, endDate, timelineContentRef, roomListRef]);

  // Process unallocated bookings when data changes
  useEffect(() => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] 🔄 UNALLOCATED BOOKINGS EFFECT TRIGGERED:`, {
      hasRawApiData: !!rawApiData,
      hasRealData,
      rawApiDataKeys: rawApiData ? Object.keys(rawApiData) : [],
      currentUnallocatedCount: unallocatedBookings.length,
    });

    if (rawApiData && hasRealData) {
      console.log(
        `[${timestamp}] ✅ PROCESSING UNALLOCATED BOOKINGS FROM API DATA:`,
        {
          bookingsCount: rawApiData.bookings?.length || 0,
          availabilityCount: rawApiData.availability?.length || 0,
        }
      );
      // Use the raw API data from useTimelineData hook instead of making another API call
      processUnallocatedBookings(rawApiData);
    } else {
      console.log(
        `[${timestamp}] ⚠️ SKIPPING UNALLOCATED BOOKINGS PROCESSING:`,
        {
          reason: !rawApiData ? "No rawApiData" : "No real data",
          hasRawApiData: !!rawApiData,
          hasRealData,
        }
      );
    }
  }, [rawApiData, hasRealData]);

  // Debug log when unallocated bookings state changes
  useEffect(() => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] 📋 UNALLOCATED BOOKINGS STATE CHANGED:`, {
      count: unallocatedBookings.length,
      bookings: unallocatedBookings.map((b) => ({
        id: b.id,
        orderId: b.order_id,
        guestName: b.guest_name,
        fromDate: b.from_date,
        toDate: b.to_date,
        roomConfigId: b.room_config_id,
      })),
    });
  }, [unallocatedBookings]);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex flex-col h-screen bg-background">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
            <div className="text-foreground">Loading room availability...</div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex flex-col h-screen bg-background">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="text-destructive mb-2">
              Error loading availability data
            </div>
            <div className="text-muted-foreground text-sm">{error}</div>
            {!hasRealData && (
              <div className="text-muted-foreground text-sm mt-2">
                Falling back to demo data
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-background">
      <Toaster />
      {/* Main Timeline Content */}
      {/* Collapsible Filter Section */}
      <div className="bg-card border-b border-border py-6 px-6 flex-shrink-0 ">
        <FilterControls
          selectedRoomTypes={selectedRoomTypes}
          onRoomTypesChange={setSelectedRoomTypes}
          selectedRooms={selectedRooms}
          onRoomsChange={setSelectedRooms}
          selectedStatuses={selectedStatuses}
          onStatusesChange={setSelectedStatuses}
          startDate={startDate}
          onStartDateChange={setStartDate}
          endDate={endDate}
          onEndDateChange={setEndDate}
          roomTypes={roomTypes}
          roomTypeConfigs={roomTypeConfigs}
          rooms={rooms}
          onApplyDateRange={handleApplyDateRange}
          isLoading={isLoading}
        />
      </div>

      {/* Timeline Container */}
      <div className="flex-1 flex overflow-hidden">
        {/* Room List */}
        <div className="w-64 bg-primary border-r border-border flex flex-col">
          <div className="h-16 border-b border-border bg-muted/50 flex items-center px-6 flex-shrink-0">
            <h3 className="font-semibold text-foreground text-base">
              Rooms ({Object.values(groupedRooms).flat().length})
            </h3>
          </div>

          <RoomList
            groupedRooms={groupedRooms}
            hoveredRoomId={hoveredRoomId}
            onHover={setHoveredRoomId}
            onScroll={handleRoomListScroll}
            ref={roomListRef}
          />
        </div>

        {/* Timeline Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <div
            ref={timelineContentRef}
            className="flex-1 overflow-x-auto overflow-y-auto timeline-scroll"
            onScroll={handleTimelineScroll}
            style={{
              height: "calc(100vh - 140px)",
              width: "100%",
            }}
          >
            <div
              key={`timeline-${startDate.getTime()}-${endDate.getTime()}`}
              className="timeline-content"
              style={{
                width: `${TIMELINE_WIDTH}px`,
                minHeight: "100%",
                minWidth: `${TIMELINE_WIDTH}px`,
                display: "block",
                position: "relative",
              }}
            >
              <div className="timeline-header sticky top-0 z-30 bg-card border-b border-border shadow-sm">
                <TimelineHeader
                  key={`header-${dateSlots.length}-${startDate.getTime()}`}
                  dateSlots={dateSlots}
                  currentDate={startDate}
                  timelineWidth={TIMELINE_WIDTH}
                />
              </div>

              <div className="relative">
                {Object.entries(groupedRooms).map(([type, rooms]) => (
                  <div key={type}>
                    <div className="bg-muted/30 border-b border-border sticky top-0 z-10 flex items-center px-6 min-h-12 py-3">
                      <h4 className="text-sm font-semibold text-muted-foreground uppercase tracking-wider leading-tight">
                        {type} ({rooms.length})
                      </h4>
                    </div>
                    {rooms.map((room, roomIndex) => (
                      <RoomRow
                        key={`${type}-${
                          room.id
                        }-${roomIndex}-${startDate.getTime()}-${endDate.getTime()}`}
                        room={room}
                        bookings={weekBookings.filter(
                          (b) => b.room_id === room.id
                        )}
                        availableBlocks={availableBlocks}
                        dateSlots={dateSlots}
                        startDate={startDate}
                        endDate={endDate}
                        timelineWidth={TIMELINE_WIDTH}
                        isHighlighted={hoveredRoomId === room.id}
                        onHover={setHoveredRoomId}
                        hasStatusFilter={selectedStatuses.length > 0}
                        isAvailableFilterActive={selectedStatuses.includes(
                          RoomInventoryStatus.AVAILABLE
                        )}
                        onViewBooking={handleViewBooking}
                        onUpdateStatus={handleUpdateStatus}
                        onMoveToUnallocated={handleMoveToUnallocated}
                        onSplitBooking={handleSplitBooking}
                        onBookingClick={handleBookingClick}
                        onMarkAsMaintenance={handleMarkAsMaintenance}
                        onMarkAsAvailable={handleMarkAsAvailable}
                        selectedBookingId={selectedTimelineBookingId}
                      />
                    ))}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer showing actual selected range */}
      <TimelineFooter
        startDate={startDate}
        endDate={endDate}
        dateSlots={dateSlots}
        groupedRooms={groupedRooms}
        weekBookings={weekBookings}
        timelineWidth={TIMELINE_WIDTH}
      />

      {/* Floating Action Button for Unallocated Bookings */}
      <UnallocatedBookingsButton
        count={unallocatedBookings.length}
        onClick={() => setIsUnallocatedDrawerOpen(true)}
      />

      {/* Unallocated Bookings Component */}
      <UnallocatedBookings
        unallocatedBookings={unallocatedBookings}
        isLoading={isLoading}
        isOpen={isUnallocatedDrawerOpen}
        onOpenChange={setIsUnallocatedDrawerOpen}
        onAllocateRoom={handleAssignRoom}
      />

      {/* Booking Detail Drawer */}
      <Drawer open={isBookingDrawerOpen} onOpenChange={setIsBookingDrawerOpen}>
        <Drawer.Content>
          <Drawer.Header>
            <Drawer.Title>Booking Details</Drawer.Title>
            <Drawer.Description>
              View and manage booking information
            </Drawer.Description>
          </Drawer.Header>
          <Drawer.Body className="overflow-y-auto p-0 m-0">
            {selectedBookingId && (
              <SimpleBookingDetail
                bookingId={selectedBookingId}
                isInSidebar={true}
              />
            )}
          </Drawer.Body>
          <Drawer.Footer className="flex justify-end gap-2 p-6 border-t">
            <Button
              variant="secondary"
              onClick={() => {
                setIsBookingDrawerOpen(false);
                setSelectedBookingId(null);
              }}
            >
              Close
            </Button>
          </Drawer.Footer>
        </Drawer.Content>
      </Drawer>

      {/* Update Status Drawer */}
      <Drawer
        open={isUpdateStatusDrawerOpen}
        onOpenChange={setIsUpdateStatusDrawerOpen}
      >
        <Drawer.Content>
          <Drawer.Header>
            <Drawer.Title>Update Booking Status</Drawer.Title>
            <Drawer.Description>
              Update the status, dates, and notes for this booking
            </Drawer.Description>
          </Drawer.Header>
          <Drawer.Body className="p-6 space-y-4">
            {selectedBookingForUpdate && (
              <>
                <div>
                  <Text className="text-sm font-medium mb-2">Booking ID</Text>
                  <Text className="text-sm text-muted-foreground">
                    {selectedBookingForUpdate.order_id ||
                      selectedBookingForUpdate.id}
                  </Text>
                </div>

                <div>
                  <Text className="text-sm font-medium mb-2">Status</Text>
                  <Select
                    value={updateForm.status}
                    onValueChange={(value) =>
                      setUpdateForm((prev) => ({ ...prev, status: value }))
                    }
                  >
                    <Select.Trigger>
                      <Select.Value placeholder="Select status" />
                    </Select.Trigger>
                    <Select.Content>
                      {Object.values(RoomInventoryStatus).map((status) => (
                        <Select.Item key={status} value={status}>
                          {status.charAt(0).toUpperCase() +
                            status.slice(1).replace("_", " ")}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Text className="text-sm font-medium mb-2">From Date</Text>
                    <Input
                      type="date"
                      value={updateForm.fromDate}
                      onChange={(e) =>
                        setUpdateForm((prev) => ({
                          ...prev,
                          fromDate: e.target.value,
                        }))
                      }
                    />
                  </div>
                  <div>
                    <Text className="text-sm font-medium mb-2">To Date</Text>
                    <Input
                      type="date"
                      value={updateForm.toDate}
                      onChange={(e) =>
                        setUpdateForm((prev) => ({
                          ...prev,
                          toDate: e.target.value,
                        }))
                      }
                    />
                  </div>
                </div>

                <div>
                  <Text className="text-sm font-medium mb-2">Notes</Text>
                  <Input
                    value={updateForm.notes}
                    onChange={(e) =>
                      setUpdateForm((prev) => ({
                        ...prev,
                        notes: e.target.value,
                      }))
                    }
                    placeholder="Add notes..."
                  />
                </div>
              </>
            )}
          </Drawer.Body>
          <Drawer.Footer className="flex justify-end gap-2 p-6 border-t">
            <Button
              variant="secondary"
              onClick={() => {
                setIsUpdateStatusDrawerOpen(false);
                setSelectedBookingForUpdate(null);
              }}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleUpdateBooking}
              disabled={isUpdatingBooking}
            >
              {isUpdatingBooking ? "Updating..." : "Update Booking"}
            </Button>
          </Drawer.Footer>
        </Drawer.Content>
      </Drawer>

      {/* Room Allocation Modal */}
      <Drawer
        open={isAllocationModalOpen}
        onOpenChange={setIsAllocationModalOpen}
      >
        <Drawer.Content>
          <Drawer.Header>
            <Drawer.Title>Assign Room</Drawer.Title>
            <Drawer.Description>
              Select an available room for this booking
            </Drawer.Description>
          </Drawer.Header>
          <Drawer.Body className="p-6 space-y-4">
            {selectedBookingForAllocation && (
              <>
                <div>
                  <Text className="text-sm font-medium mb-2">
                    Booking Details
                  </Text>
                  <div className="bg-muted/50 p-3 rounded-lg">
                    <div className="text-sm">
                      <strong>Booking ID:</strong>{" "}
                      {selectedBookingForAllocation.order_id ||
                        selectedBookingForAllocation.id}
                    </div>
                    {selectedBookingForAllocation.guest_name && (
                      <div className="text-sm mt-1">
                        <strong>Guest:</strong>{" "}
                        {selectedBookingForAllocation.guest_name}
                      </div>
                    )}
                    <div className="text-sm mt-1">
                      <strong>Dates:</strong>{" "}
                      {format(
                        selectedBookingForAllocation.metadata.check_in_date,
                        "MMM dd, yyyy"
                      )}{" "}
                      -{" "}
                      {format(
                        selectedBookingForAllocation.metadata.check_out_date,
                        "MMM dd, yyyy"
                      )}
                    </div>
                  </div>
                </div>

                <div>
                  <Text className="text-sm font-medium mb-2">
                    Available Rooms
                  </Text>
                  {availableRoomsForAllocation.length === 0 ? (
                    <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg">
                      No available rooms found for the selected dates and room
                      type.
                    </div>
                  ) : (
                    <Select
                      value={selectedTargetRoomId}
                      onValueChange={setSelectedTargetRoomId}
                    >
                      <Select.Trigger>
                        <Select.Value placeholder="Select a room" />
                      </Select.Trigger>
                      <Select.Content>
                        {availableRoomsForAllocation.map((room) => (
                          <Select.Item key={room.id} value={room.id}>
                            {room.title} - {room.metadata?.room_number || "N/A"}
                            {room.metadata?.floor &&
                              ` (Floor ${room.metadata.floor})`}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  )}
                </div>
              </>
            )}
          </Drawer.Body>
          <Drawer.Footer className="flex justify-end gap-2 p-6 border-t">
            <Button
              variant="secondary"
              onClick={() => {
                setIsAllocationModalOpen(false);
                setSelectedBookingForAllocation(null);
                setSelectedTargetRoomId("");
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAllocateRoom}
              disabled={
                !selectedTargetRoomId ||
                availableRoomsForAllocation.length === 0
              }
            >
              Assign Room
            </Button>
          </Drawer.Footer>
        </Drawer.Content>
      </Drawer>

      {/* Split Booking Modal */}
      <SplitBookingModal
        isOpen={isSplitBookingModalOpen}
        onOpenChange={setIsSplitBookingModalOpen}
        booking={selectedBookingForSplit}
        room={selectedRoomForSplit}
        onSplitSuccess={handleSplitBookingSuccess}
      />
    </div>
  );
};

export default TimelineView;
